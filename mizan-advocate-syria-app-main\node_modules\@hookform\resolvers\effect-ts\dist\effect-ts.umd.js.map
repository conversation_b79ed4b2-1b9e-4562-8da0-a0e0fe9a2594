{"version": 3, "file": "effect-ts.umd.js", "sources": ["../src/effect-ts.ts"], "sourcesContent": ["import { formatIssue } from '@effect/schema/ArrayFormatter';\nimport { decodeUnknown } from '@effect/schema/ParseResult';\nimport { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport * as Effect from 'effect/Effect';\nimport type { FieldErrors } from 'react-hook-form';\nimport type { Resolver } from './types';\n\nexport const effectTsResolver: Resolver =\n  (schema, config = { errors: 'all', onExcessProperty: 'ignore' }) =>\n  (values, _, options) => {\n    return decodeUnknown(\n      schema,\n      config,\n    )(values).pipe(\n      Effect.catchAll((parseIssue) => Effect.flip(formatIssue(parseIssue))),\n      Effect.mapError((issues) => {\n        const errors = issues.reduce((acc, current) => {\n          const key = current.path.join('.');\n          acc[key] = { message: current.message, type: current._tag };\n          return acc;\n        }, {} as FieldErrors);\n\n        return toNestErrors(errors, options);\n      }),\n      Effect.tap(() =>\n        Effect.sync(\n          () =>\n            options.shouldUseNativeValidation &&\n            validateFieldsNatively({}, options),\n        ),\n      ),\n      Effect.match({\n        onFailure: (errors) => ({ errors, values: {} }),\n        onSuccess: (result) => ({ errors: {}, values: result }),\n      }),\n      Effect.runPromise,\n    );\n  };\n"], "names": ["schema", "config", "errors", "onExcessProperty", "values", "_", "options", "decodeUnknown", "pipe", "Effect", "catchAll", "parseIssue", "flip", "formatIssue", "mapError", "issues", "reduce", "acc", "current", "path", "join", "message", "type", "_tag", "toNestErrors", "tap", "sync", "shouldUseNativeValidation", "validateFieldsNatively", "match", "onFailure", "onSuccess", "result", "runPromise"], "mappings": "q3BAQE,SAACA,EAAQC,GAAsD,YAAtDA,IAAAA,IAAAA,EAAS,CAAEC,OAAQ,MAAOC,iBAAkB,WACpDC,SAAAA,EAAQC,EAAGC,GACV,OAAOC,EAAAA,cACLP,EACAC,EAFKM,CAGLH,GAAQI,KACRC,EAAOC,SAAS,SAACC,GAAe,OAAAF,EAAOG,KAAKC,EAAWA,YAACF,GAAY,GACpEF,EAAOK,SAAS,SAACC,GACf,IAAMb,EAASa,EAAOC,OAAO,SAACC,EAAKC,GAGjC,OADAD,EADYC,EAAQC,KAAKC,KAAK,MACnB,CAAEC,QAASH,EAAQG,QAASC,KAAMJ,EAAQK,MAC9CN,CACT,EAAG,CAAA,GAEH,OAAOO,EAAYA,aAACtB,EAAQI,EAC9B,GACAG,EAAOgB,IAAI,WACT,OAAAhB,EAAOiB,KACL,WACE,OAAApB,EAAQqB,2BACRC,yBAAuB,CAAA,EAAItB,EAAQ,EACtC,GAEHG,EAAOoB,MAAM,CACXC,UAAW,SAAC5B,GAAY,MAAA,CAAEA,OAAAA,EAAQE,OAAQ,CAAA,EAAI,EAC9C2B,UAAW,SAACC,GAAM,MAAM,CAAE9B,OAAQ,CAAA,EAAIE,OAAQ4B,EAAQ,IAExDvB,EAAOwB,WAEX,CAAC"}