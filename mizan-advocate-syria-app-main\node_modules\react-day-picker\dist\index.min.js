!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("react/jsx-runtime"),require("react"),require("date-fns"),require("date-fns/locale")):"function"==typeof define&&define.amd?define(["exports","react/jsx-runtime","react","date-fns","date-fns/locale"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).DatePicker={},e.jsxRuntime,e.React,e.DateFns,e.DateFnsLocale)}(this,(function(e,n,t,a,o){"use strict";var r=function(){return r=Object.assign||function(e){for(var n,t=1,a=arguments.length;t<a;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},r.apply(this,arguments)};function i(e,n,t){if(t||2===arguments.length)for(var a,o=0,r=n.length;o<r;o++)!a&&o in n||(a||(a=Array.prototype.slice.call(n,0,o)),a[o]=n[o]);return e.concat(a||Array.prototype.slice.call(n))}function s(e){return"multiple"===e.mode}function l(e){return"range"===e.mode}function d(e){return"single"===e.mode}"function"==typeof SuppressedError&&SuppressedError;var u={root:"rdp",multiple_months:"rdp-multiple_months",with_weeknumber:"rdp-with_weeknumber",vhidden:"rdp-vhidden",button_reset:"rdp-button_reset",button:"rdp-button",caption:"rdp-caption",caption_start:"rdp-caption_start",caption_end:"rdp-caption_end",caption_between:"rdp-caption_between",caption_label:"rdp-caption_label",caption_dropdowns:"rdp-caption_dropdowns",dropdown:"rdp-dropdown",dropdown_month:"rdp-dropdown_month",dropdown_year:"rdp-dropdown_year",dropdown_icon:"rdp-dropdown_icon",months:"rdp-months",month:"rdp-month",table:"rdp-table",tbody:"rdp-tbody",tfoot:"rdp-tfoot",head:"rdp-head",head_row:"rdp-head_row",head_cell:"rdp-head_cell",nav:"rdp-nav",nav_button:"rdp-nav_button",nav_button_previous:"rdp-nav_button_previous",nav_button_next:"rdp-nav_button_next",nav_icon:"rdp-nav_icon",row:"rdp-row",weeknumber:"rdp-weeknumber",cell:"rdp-cell",day:"rdp-day",day_today:"rdp-day_today",day_outside:"rdp-day_outside",day_selected:"rdp-day_selected",day_disabled:"rdp-day_disabled",day_hidden:"rdp-day_hidden",day_range_start:"rdp-day_range_start",day_range_end:"rdp-day_range_end",day_range_middle:"rdp-day_range_middle"};var c=Object.freeze({__proto__:null,formatCaption:function(e,n){return a.format(e,"LLLL y",n)},formatDay:function(e,n){return a.format(e,"d",n)},formatMonthCaption:function(e,n){return a.format(e,"LLLL",n)},formatWeekNumber:function(e){return"".concat(e)},formatWeekdayName:function(e,n){return a.format(e,"cccccc",n)},formatYearCaption:function(e,n){return a.format(e,"yyyy",n)}}),f=Object.freeze({__proto__:null,labelDay:function(e,n,t){return a.format(e,"do MMMM (EEEE)",t)},labelMonthDropdown:function(){return"Month: "},labelNext:function(){return"Go to next month"},labelPrevious:function(){return"Go to previous month"},labelWeekNumber:function(e){return"Week n. ".concat(e)},labelWeekday:function(e,n){return a.format(e,"cccc",n)},labelYearDropdown:function(){return"Year: "}});function v(e){var n=e.fromYear,t=e.toYear,o=e.fromMonth,r=e.toMonth,i=e.fromDate,s=e.toDate;return o?i=a.startOfMonth(o):n&&(i=new Date(n,0,1)),r?s=a.endOfMonth(r):t&&(s=new Date(t,11,31)),{fromDate:i?a.startOfDay(i):void 0,toDate:s?a.startOfDay(s):void 0}}var p=t.createContext(void 0);function h(e){var t,a,i,h,y,m=e.initialProps,b=(a=u,i=o.enUS,h=new Date,{captionLayout:"buttons",classNames:a,formatters:c,labels:f,locale:i,modifiersClassNames:{},modifiers:{},numberOfMonths:1,styles:{},today:h,mode:"default"}),x=v(m),D=x.fromDate,M=x.toDate,g=null!==(t=m.captionLayout)&&void 0!==t?t:b.captionLayout;"buttons"===g||D&&M||(g="buttons"),(d(m)||s(m)||l(m))&&(y=m.onSelect);var w=r(r(r({},b),m),{captionLayout:g,classNames:r(r({},b.classNames),m.classNames),components:r({},m.components),formatters:r(r({},b.formatters),m.formatters),fromDate:D,labels:r(r({},b.labels),m.labels),mode:m.mode||b.mode,modifiers:r(r({},b.modifiers),m.modifiers),modifiersClassNames:r(r({},b.modifiersClassNames),m.modifiersClassNames),onSelect:y,styles:r(r({},b.styles),m.styles),toDate:M});return n.jsx(p.Provider,{value:w,children:e.children})}function y(){var e=t.useContext(p);if(!e)throw new Error("useDayPicker must be used within a DayPickerProvider.");return e}function m(e){var t=y(),a=t.locale,o=t.classNames,r=t.styles,i=t.formatters.formatCaption;return n.jsx("div",{className:o.caption_label,style:r.caption_label,"aria-live":"polite",role:"presentation",id:e.id,children:i(e.displayMonth,{locale:a})})}function b(e){return n.jsx("svg",r({width:"8px",height:"8px",viewBox:"0 0 120 120","data-testid":"iconDropdown"},e,{children:n.jsx("path",{d:"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.**********,59.4824074 -0.**********,52.5175926 4.22182541,48.2218254 Z",fill:"currentColor",fillRule:"nonzero"})}))}function x(e){var t,a,o=e.onChange,r=e.value,i=e.children,s=e.caption,l=e.className,d=e.style,u=y(),c=null!==(a=null===(t=u.components)||void 0===t?void 0:t.IconDropdown)&&void 0!==a?a:b;return n.jsxs("div",{className:l,style:d,children:[n.jsx("span",{className:u.classNames.vhidden,children:e["aria-label"]}),n.jsx("select",{name:e.name,"aria-label":e["aria-label"],className:u.classNames.dropdown,style:u.styles.dropdown,value:r,onChange:o,children:i}),n.jsxs("div",{className:u.classNames.caption_label,style:u.styles.caption_label,"aria-hidden":"true",children:[s,n.jsx(c,{className:u.classNames.dropdown_icon,style:u.styles.dropdown_icon})]})]})}function D(e){var t,o=y(),r=o.fromDate,i=o.toDate,s=o.styles,l=o.locale,d=o.formatters.formatMonthCaption,u=o.classNames,c=o.components,f=o.labels.labelMonthDropdown;if(!r)return n.jsx(n.Fragment,{});if(!i)return n.jsx(n.Fragment,{});var v=[];if(a.isSameYear(r,i))for(var p=a.startOfMonth(r),h=r.getMonth();h<=i.getMonth();h++)v.push(a.setMonth(p,h));else for(p=a.startOfMonth(new Date),h=0;h<=11;h++)v.push(a.setMonth(p,h));var m=null!==(t=null==c?void 0:c.Dropdown)&&void 0!==t?t:x;return n.jsx(m,{name:"months","aria-label":f(),className:u.dropdown_month,style:s.dropdown_month,onChange:function(n){var t=Number(n.target.value),o=a.setMonth(a.startOfMonth(e.displayMonth),t);e.onChange(o)},value:e.displayMonth.getMonth(),caption:d(e.displayMonth,{locale:l}),children:v.map((function(e){return n.jsx("option",{value:e.getMonth(),children:d(e,{locale:l})},e.getMonth())}))})}function M(e){var t,o=e.displayMonth,r=y(),i=r.fromDate,s=r.toDate,l=r.locale,d=r.styles,u=r.classNames,c=r.components,f=r.formatters.formatYearCaption,v=r.labels.labelYearDropdown,p=[];if(!i)return n.jsx(n.Fragment,{});if(!s)return n.jsx(n.Fragment,{});for(var h=i.getFullYear(),m=s.getFullYear(),b=h;b<=m;b++)p.push(a.setYear(a.startOfYear(new Date),b));var D=null!==(t=null==c?void 0:c.Dropdown)&&void 0!==t?t:x;return n.jsx(D,{name:"years","aria-label":v(),className:u.dropdown_year,style:d.dropdown_year,onChange:function(n){var t=a.setYear(a.startOfMonth(o),Number(n.target.value));e.onChange(t)},value:o.getFullYear(),caption:f(o,{locale:l}),children:p.map((function(e){return n.jsx("option",{value:e.getFullYear(),children:f(e,{locale:l})},e.getFullYear())}))})}function g(){var e=y(),n=function(e){var n=e.month,t=e.defaultMonth,o=e.today,r=n||t||o||new Date,i=e.toDate,s=e.fromDate,l=e.numberOfMonths,d=void 0===l?1:l;if(i&&a.differenceInCalendarMonths(i,r)<0){var u=-1*(d-1);r=a.addMonths(i,u)}return s&&a.differenceInCalendarMonths(r,s)<0&&(r=s),a.startOfMonth(r)}(e),o=function(e,n){var a=t.useState(e),o=a[0];return[void 0===n?o:n,a[1]]}(n,e.month),r=o[0],i=o[1];return[r,function(n){var t;if(!e.disableNavigation){var o=a.startOfMonth(n);i(o),null===(t=e.onMonthChange)||void 0===t||t.call(e,o)}}]}var w=t.createContext(void 0);function k(e){var t=y(),o=g(),r=o[0],i=o[1],s=function(e,n){for(var t=n.reverseMonths,o=n.numberOfMonths,r=a.startOfMonth(e),i=a.startOfMonth(a.addMonths(r,o)),s=a.differenceInCalendarMonths(i,r),l=[],d=0;d<s;d++){var u=a.addMonths(r,d);l.push(u)}return t&&(l=l.reverse()),l}(r,t),l=function(e,n){if(!n.disableNavigation){var t=n.toDate,o=n.pagedNavigation,r=n.numberOfMonths,i=void 0===r?1:r,s=o?i:1,l=a.startOfMonth(e);if(!t)return a.addMonths(l,s);if(!(a.differenceInCalendarMonths(t,e)<i))return a.addMonths(l,s)}}(r,t),d=function(e,n){if(!n.disableNavigation){var t=n.fromDate,o=n.pagedNavigation,r=n.numberOfMonths,i=o?void 0===r?1:r:1,s=a.startOfMonth(e);if(!t)return a.addMonths(s,-i);if(!(a.differenceInCalendarMonths(s,t)<=0))return a.addMonths(s,-i)}}(r,t),u=function(e){return s.some((function(n){return a.isSameMonth(e,n)}))},c={currentMonth:r,displayMonths:s,goToMonth:i,goToDate:function(e,n){u(e)||(n&&a.isBefore(e,n)?i(a.addMonths(e,1+-1*t.numberOfMonths)):i(e))},previousMonth:d,nextMonth:l,isDateDisplayed:u};return n.jsx(w.Provider,{value:c,children:e.children})}function _(){var e=t.useContext(w);if(!e)throw new Error("useNavigation must be used within a NavigationProvider");return e}function j(e){var t,o=y(),r=o.classNames,i=o.styles,s=o.components,l=_().goToMonth,d=function(n){l(a.addMonths(n,e.displayIndex?-e.displayIndex:0))},u=null!==(t=null==s?void 0:s.CaptionLabel)&&void 0!==t?t:m,c=n.jsx(u,{id:e.id,displayMonth:e.displayMonth});return n.jsxs("div",{className:r.caption_dropdowns,style:i.caption_dropdowns,children:[n.jsx("div",{className:r.vhidden,children:c}),n.jsx(D,{onChange:d,displayMonth:e.displayMonth}),n.jsx(M,{onChange:d,displayMonth:e.displayMonth})]})}function N(e){return n.jsx("svg",r({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:n.jsx("path",{d:"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z",fill:"currentColor",fillRule:"nonzero"})}))}function C(e){return n.jsx("svg",r({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:n.jsx("path",{d:"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z",fill:"currentColor"})}))}var S=t.forwardRef((function(e,t){var a=y(),o=a.classNames,i=a.styles,s=[o.button_reset,o.button];e.className&&s.push(e.className);var l=s.join(" "),d=r(r({},i.button_reset),i.button);return e.style&&Object.assign(d,e.style),n.jsx("button",r({},e,{ref:t,type:"button",className:l,style:d}))}));function O(e){var t,a,o=y(),r=o.dir,i=o.locale,s=o.classNames,l=o.styles,d=o.labels,u=d.labelPrevious,c=d.labelNext,f=o.components;if(!e.nextMonth&&!e.previousMonth)return n.jsx(n.Fragment,{});var v=u(e.previousMonth,{locale:i}),p=[s.nav_button,s.nav_button_previous].join(" "),h=c(e.nextMonth,{locale:i}),m=[s.nav_button,s.nav_button_next].join(" "),b=null!==(t=null==f?void 0:f.IconRight)&&void 0!==t?t:C,x=null!==(a=null==f?void 0:f.IconLeft)&&void 0!==a?a:N;return n.jsxs("div",{className:s.nav,style:l.nav,children:[!e.hidePrevious&&n.jsx(S,{name:"previous-month","aria-label":v,className:p,style:l.nav_button_previous,disabled:!e.previousMonth,onClick:e.onPreviousClick,children:"rtl"===r?n.jsx(b,{className:s.nav_icon,style:l.nav_icon}):n.jsx(x,{className:s.nav_icon,style:l.nav_icon})}),!e.hideNext&&n.jsx(S,{name:"next-month","aria-label":h,className:m,style:l.nav_button_next,disabled:!e.nextMonth,onClick:e.onNextClick,children:"rtl"===r?n.jsx(x,{className:s.nav_icon,style:l.nav_icon}):n.jsx(b,{className:s.nav_icon,style:l.nav_icon})})]})}function P(e){var t=y().numberOfMonths,o=_(),r=o.previousMonth,i=o.nextMonth,s=o.goToMonth,l=o.displayMonths,d=l.findIndex((function(n){return a.isSameMonth(e.displayMonth,n)})),u=0===d,c=d===l.length-1,f=t>1&&(u||!c),v=t>1&&(c||!u);return n.jsx(O,{displayMonth:e.displayMonth,hideNext:f,hidePrevious:v,nextMonth:i,previousMonth:r,onPreviousClick:function(){r&&s(r)},onNextClick:function(){i&&s(i)}})}function I(e){var t,a,o=y(),r=o.classNames,i=o.disableNavigation,s=o.styles,l=o.captionLayout,d=o.components,u=null!==(t=null==d?void 0:d.CaptionLabel)&&void 0!==t?t:m;return a=i?n.jsx(u,{id:e.id,displayMonth:e.displayMonth}):"dropdown"===l?n.jsx(j,{displayMonth:e.displayMonth,id:e.id}):"dropdown-buttons"===l?n.jsxs(n.Fragment,{children:[n.jsx(j,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id}),n.jsx(P,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id})]}):n.jsxs(n.Fragment,{children:[n.jsx(u,{id:e.id,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),n.jsx(P,{displayMonth:e.displayMonth,id:e.id})]}),n.jsx("div",{className:r.caption,style:s.caption,children:a})}function W(e){var t=y(),a=t.footer,o=t.styles,r=t.classNames.tfoot;return a?n.jsx("tfoot",{className:r,style:o.tfoot,children:n.jsx("tr",{children:n.jsx("td",{colSpan:8,children:a})})}):n.jsx(n.Fragment,{})}function L(){var e=y(),t=e.classNames,o=e.styles,r=e.showWeekNumber,i=e.locale,s=e.weekStartsOn,l=e.ISOWeek,d=e.formatters.formatWeekdayName,u=e.labels.labelWeekday,c=function(e,n,t){for(var o=t?a.startOfISOWeek(new Date):a.startOfWeek(new Date,{locale:e,weekStartsOn:n}),r=[],i=0;i<7;i++){var s=a.addDays(o,i);r.push(s)}return r}(i,s,l);return n.jsxs("tr",{style:o.head_row,className:t.head_row,children:[r&&n.jsx("td",{style:o.head_cell,className:t.head_cell}),c.map((function(e,a){return n.jsx("th",{scope:"col",className:t.head_cell,style:o.head_cell,"aria-label":u(e,{locale:i}),children:d(e,{locale:i})},a)}))]})}function E(){var e,t=y(),a=t.classNames,o=t.styles,r=t.components,i=null!==(e=null==r?void 0:r.HeadRow)&&void 0!==e?e:L;return n.jsx("thead",{style:o.head,className:a.head,children:n.jsx(i,{})})}function B(e){var t=y(),a=t.locale,o=t.formatters.formatDay;return n.jsx(n.Fragment,{children:o(e.date,{locale:a})})}var F=t.createContext(void 0);function R(e){if(!s(e.initialProps)){var t={selected:void 0,modifiers:{disabled:[]}};return n.jsx(F.Provider,{value:t,children:e.children})}return n.jsx(T,{initialProps:e.initialProps,children:e.children})}function T(e){var t=e.initialProps,o=e.children,r=t.selected,s=t.min,l=t.max,d={disabled:[]};r&&d.disabled.push((function(e){var n=l&&r.length>l-1,t=r.some((function(n){return a.isSameDay(n,e)}));return Boolean(n&&!t)}));var u={selected:r,onDayClick:function(e,n,o){var d,u;if((null===(d=t.onDayClick)||void 0===d||d.call(t,e,n,o),!Boolean(n.selected&&s&&(null==r?void 0:r.length)===s))&&!Boolean(!n.selected&&l&&(null==r?void 0:r.length)===l)){var c=r?i([],r,!0):[];if(n.selected){var f=c.findIndex((function(n){return a.isSameDay(e,n)}));c.splice(f,1)}else c.push(e);null===(u=t.onSelect)||void 0===u||u.call(t,c,e,n,o)}},modifiers:d};return n.jsx(F.Provider,{value:u,children:o})}function A(){var e=t.useContext(F);if(!e)throw new Error("useSelectMultiple must be used within a SelectMultipleProvider");return e}function Y(e,n){var t=n||{},o=t.from,r=t.to;if(o&&r){if(a.isSameDay(r,e)&&a.isSameDay(o,e))return;if(a.isSameDay(r,e))return{from:r,to:void 0};if(a.isSameDay(o,e))return;return a.isAfter(o,e)?{from:e,to:r}:{from:o,to:e}}return r?a.isAfter(e,r)?{from:r,to:e}:{from:e,to:r}:o?a.isBefore(e,o)?{from:e,to:o}:{from:o,to:e}:{from:e,to:void 0}}var H,U=t.createContext(void 0);function q(e){if(!l(e.initialProps)){var t={selected:void 0,modifiers:{range_start:[],range_end:[],range_middle:[],disabled:[]}};return n.jsx(U.Provider,{value:t,children:e.children})}return n.jsx(K,{initialProps:e.initialProps,children:e.children})}function K(e){var t=e.initialProps,o=e.children,r=t.selected,i=r||{},s=i.from,l=i.to,d=t.min,u=t.max,c={range_start:[],range_end:[],range_middle:[],disabled:[]};if(s?(c.range_start=[s],l?(c.range_end=[l],a.isSameDay(s,l)||(c.range_middle=[{after:s,before:l}])):c.range_end=[s]):l&&(c.range_start=[l],c.range_end=[l]),d&&(s&&!l&&c.disabled.push({after:a.subDays(s,d-1),before:a.addDays(s,d-1)}),s&&l&&c.disabled.push({after:s,before:a.addDays(s,d-1)}),!s&&l&&c.disabled.push({after:a.subDays(l,d-1),before:a.addDays(l,d-1)})),u){if(s&&!l&&(c.disabled.push({before:a.addDays(s,1-u)}),c.disabled.push({after:a.addDays(s,u-1)})),s&&l){var f=u-(a.differenceInCalendarDays(l,s)+1);c.disabled.push({before:a.subDays(s,f)}),c.disabled.push({after:a.addDays(l,f)})}!s&&l&&(c.disabled.push({before:a.addDays(l,1-u)}),c.disabled.push({after:a.addDays(l,u-1)}))}return n.jsx(U.Provider,{value:{selected:r,onDayClick:function(e,n,a){var o,i;null===(o=t.onDayClick)||void 0===o||o.call(t,e,n,a);var s=Y(e,r);null===(i=t.onSelect)||void 0===i||i.call(t,s,e,n,a)},modifiers:c},children:o})}function z(){var e=t.useContext(U);if(!e)throw new Error("useSelectRange must be used within a SelectRangeProvider");return e}function Z(e){return Array.isArray(e)?i([],e,!0):void 0!==e?[e]:[]}e.InternalModifier=void 0,(H=e.InternalModifier||(e.InternalModifier={})).Outside="outside",H.Disabled="disabled",H.Selected="selected",H.Hidden="hidden",H.Today="today",H.RangeStart="range_start",H.RangeEnd="range_end",H.RangeMiddle="range_middle";var G=e.InternalModifier.Selected,J=e.InternalModifier.Disabled,Q=e.InternalModifier.Hidden,V=e.InternalModifier.Today,X=e.InternalModifier.RangeEnd,$=e.InternalModifier.RangeMiddle,ee=e.InternalModifier.RangeStart,ne=e.InternalModifier.Outside;var te=t.createContext(void 0);function ae(e){var t=y(),a=function(e,n,t){var a,o=((a={})[G]=Z(e.selected),a[J]=Z(e.disabled),a[Q]=Z(e.hidden),a[V]=[e.today],a[X]=[],a[$]=[],a[ee]=[],a[ne]=[],a);return e.fromDate&&o[J].push({before:e.fromDate}),e.toDate&&o[J].push({after:e.toDate}),s(e)?o[J]=o[J].concat(n.modifiers[J]):l(e)&&(o[J]=o[J].concat(t.modifiers[J]),o[ee]=t.modifiers[ee],o[$]=t.modifiers[$],o[X]=t.modifiers[X]),o}(t,A(),z()),o=function(e){var n={};return Object.entries(e).forEach((function(e){var t=e[0],a=e[1];n[t]=Z(a)})),n}(t.modifiers),i=r(r({},a),o);return n.jsx(te.Provider,{value:i,children:e.children})}function oe(){var e=t.useContext(te);if(!e)throw new Error("useModifiers must be used within a ModifiersProvider");return e}function re(e){return Boolean(e&&"object"==typeof e&&"before"in e&&"after"in e)}function ie(e){return Boolean(e&&"object"==typeof e&&"from"in e)}function se(e){return Boolean(e&&"object"==typeof e&&"after"in e)}function le(e){return Boolean(e&&"object"==typeof e&&"before"in e)}function de(e){return Boolean(e&&"object"==typeof e&&"dayOfWeek"in e)}function ue(e,n){return n.some((function(n){if("boolean"==typeof n)return n;if(t=n,a.isDate(t))return a.isSameDay(e,n);var t,o,r,i,s,l;if(function(e){return Array.isArray(e)&&e.every(a.isDate)}(n))return n.includes(e);if(ie(n))return o=e,s=(r=n).from,l=r.to,s&&l?(a.differenceInCalendarDays(l,s)<0&&(s=(i=[l,s])[0],l=i[1]),a.differenceInCalendarDays(o,s)>=0&&a.differenceInCalendarDays(l,o)>=0):l?a.isSameDay(l,o):!!s&&a.isSameDay(s,o);if(de(n))return n.dayOfWeek.includes(e.getDay());if(re(n)){var d=a.differenceInCalendarDays(n.before,e)>0,u=a.differenceInCalendarDays(n.after,e)<0;return a.isAfter(n.before,n.after)?u&&d:d||u}return se(n)?a.differenceInCalendarDays(e,n.after)>0:le(n)?a.differenceInCalendarDays(n.before,e)>0:"function"==typeof n&&n(e)}))}function ce(e,n,t){var o=Object.keys(n).reduce((function(t,a){var o=n[a];return ue(e,o)&&t.push(a),t}),[]),r={};return o.forEach((function(e){return r[e]=!0})),t&&!a.isSameMonth(e,t)&&(r.outside=!0),r}var fe=365;function ve(e,n){var t=n.moveBy,o=n.direction,i=n.context,s=n.modifiers,l=n.retry,d=void 0===l?{count:0,lastFocused:e}:l,u=i.weekStartsOn,c=i.fromDate,f=i.toDate,v=i.locale,p={day:a.addDays,week:a.addWeeks,month:a.addMonths,year:a.addYears,startOfWeek:function(e){return i.ISOWeek?a.startOfISOWeek(e):a.startOfWeek(e,{locale:v,weekStartsOn:u})},endOfWeek:function(e){return i.ISOWeek?a.endOfISOWeek(e):a.endOfWeek(e,{locale:v,weekStartsOn:u})}}[t](e,"after"===o?1:-1);"before"===o&&c?p=a.max([c,p]):"after"===o&&f&&(p=a.min([f,p]));var h=!0;if(s){var y=ce(p,s);h=!y.disabled&&!y.hidden}return h?p:d.count>fe?d.lastFocused:ve(p,{moveBy:t,direction:o,context:i,modifiers:s,retry:r(r({},d),{count:d.count+1})})}var pe=t.createContext(void 0);function he(e){var o=_(),r=oe(),i=t.useState(),s=i[0],l=i[1],d=t.useState(),u=d[0],c=d[1],f=function(e,n){for(var t,o,r=a.startOfMonth(e[0]),i=a.endOfMonth(e[e.length-1]),s=r;s<=i;){var l=ce(s,n);if(l.disabled||l.hidden)s=a.addDays(s,1);else{if(l.selected)return s;l.today&&!o&&(o=s),t||(t=s),s=a.addDays(s,1)}}return o||t}(o.displayMonths,r),v=(null!=s?s:u&&o.isDateDisplayed(u))?u:f,p=function(e){l(e)},h=y(),m=function(e,n){if(s){var t=ve(s,{moveBy:e,direction:n,context:h,modifiers:r});a.isSameDay(s,t)||(o.goToDate(t,s),p(t))}},b={focusedDay:s,focusTarget:v,blur:function(){c(s),l(void 0)},focus:p,focusDayAfter:function(){return m("day","after")},focusDayBefore:function(){return m("day","before")},focusWeekAfter:function(){return m("week","after")},focusWeekBefore:function(){return m("week","before")},focusMonthBefore:function(){return m("month","before")},focusMonthAfter:function(){return m("month","after")},focusYearBefore:function(){return m("year","before")},focusYearAfter:function(){return m("year","after")},focusStartOfWeek:function(){return m("startOfWeek","before")},focusEndOfWeek:function(){return m("endOfWeek","after")}};return n.jsx(pe.Provider,{value:b,children:e.children})}function ye(){var e=t.useContext(pe);if(!e)throw new Error("useFocusContext must be used within a FocusProvider");return e}function me(e,n){return ce(e,oe(),n)}var be=t.createContext(void 0);function xe(e){if(!d(e.initialProps)){var t={selected:void 0};return n.jsx(be.Provider,{value:t,children:e.children})}return n.jsx(De,{initialProps:e.initialProps,children:e.children})}function De(e){var t=e.initialProps,a=e.children,o={selected:t.selected,onDayClick:function(e,n,a){var o,r,i;null===(o=t.onDayClick)||void 0===o||o.call(t,e,n,a),!n.selected||t.required?null===(i=t.onSelect)||void 0===i||i.call(t,e,e,n,a):null===(r=t.onSelect)||void 0===r||r.call(t,void 0,e,n,a)}};return n.jsx(be.Provider,{value:o,children:a})}function Me(){var e=t.useContext(be);if(!e)throw new Error("useSelectSingle must be used within a SelectSingleProvider");return e}function ge(n,t){var a=[n.classNames.day];return Object.keys(t).forEach((function(t){var o=n.modifiersClassNames[t];if(o)a.push(o);else if(function(n){return Object.values(e.InternalModifier).includes(n)}(t)){var r=n.classNames["day_".concat(t)];r&&a.push(r)}})),a}function we(e,o,i){var u,c,f,v=y(),p=ye(),h=me(e,o),m=function(e,n){var t=y(),a=Me(),o=A(),r=z(),i=ye(),u=i.focusDayAfter,c=i.focusDayBefore,f=i.focusWeekAfter,v=i.focusWeekBefore,p=i.blur,h=i.focus,m=i.focusMonthBefore,b=i.focusMonthAfter,x=i.focusYearBefore,D=i.focusYearAfter,M=i.focusStartOfWeek,g=i.focusEndOfWeek,w={onClick:function(i){var u,c,f,v;d(t)?null===(u=a.onDayClick)||void 0===u||u.call(a,e,n,i):s(t)?null===(c=o.onDayClick)||void 0===c||c.call(o,e,n,i):l(t)?null===(f=r.onDayClick)||void 0===f||f.call(r,e,n,i):null===(v=t.onDayClick)||void 0===v||v.call(t,e,n,i)},onFocus:function(a){var o;h(e),null===(o=t.onDayFocus)||void 0===o||o.call(t,e,n,a)},onBlur:function(a){var o;p(),null===(o=t.onDayBlur)||void 0===o||o.call(t,e,n,a)},onKeyDown:function(a){var o;switch(a.key){case"ArrowLeft":a.preventDefault(),a.stopPropagation(),"rtl"===t.dir?u():c();break;case"ArrowRight":a.preventDefault(),a.stopPropagation(),"rtl"===t.dir?c():u();break;case"ArrowDown":a.preventDefault(),a.stopPropagation(),f();break;case"ArrowUp":a.preventDefault(),a.stopPropagation(),v();break;case"PageUp":a.preventDefault(),a.stopPropagation(),a.shiftKey?x():m();break;case"PageDown":a.preventDefault(),a.stopPropagation(),a.shiftKey?D():b();break;case"Home":a.preventDefault(),a.stopPropagation(),M();break;case"End":a.preventDefault(),a.stopPropagation(),g()}null===(o=t.onDayKeyDown)||void 0===o||o.call(t,e,n,a)},onKeyUp:function(a){var o;null===(o=t.onDayKeyUp)||void 0===o||o.call(t,e,n,a)},onMouseEnter:function(a){var o;null===(o=t.onDayMouseEnter)||void 0===o||o.call(t,e,n,a)},onMouseLeave:function(a){var o;null===(o=t.onDayMouseLeave)||void 0===o||o.call(t,e,n,a)},onPointerEnter:function(a){var o;null===(o=t.onDayPointerEnter)||void 0===o||o.call(t,e,n,a)},onPointerLeave:function(a){var o;null===(o=t.onDayPointerLeave)||void 0===o||o.call(t,e,n,a)},onTouchCancel:function(a){var o;null===(o=t.onDayTouchCancel)||void 0===o||o.call(t,e,n,a)},onTouchEnd:function(a){var o;null===(o=t.onDayTouchEnd)||void 0===o||o.call(t,e,n,a)},onTouchMove:function(a){var o;null===(o=t.onDayTouchMove)||void 0===o||o.call(t,e,n,a)},onTouchStart:function(a){var o;null===(o=t.onDayTouchStart)||void 0===o||o.call(t,e,n,a)}};return w}(e,h),b=function(){var e=y(),n=Me(),t=A(),a=z();return d(e)?n.selected:s(e)?t.selected:l(e)?a.selected:void 0}(),x=Boolean(v.onDayClick||"default"!==v.mode);t.useEffect((function(){var n;h.outside||p.focusedDay&&x&&a.isSameDay(p.focusedDay,e)&&(null===(n=i.current)||void 0===n||n.focus())}),[p.focusedDay,e,i,x,h.outside]);var D=ge(v,h).join(" "),M=function(e,n){var t=r({},e.styles.day);return Object.keys(n).forEach((function(n){var a;t=r(r({},t),null===(a=e.modifiersStyles)||void 0===a?void 0:a[n])})),t}(v,h),g=Boolean(h.outside&&!v.showOutsideDays||h.hidden),w=null!==(f=null===(c=v.components)||void 0===c?void 0:c.DayContent)&&void 0!==f?f:B,k={style:M,className:D,children:n.jsx(w,{date:e,displayMonth:o,activeModifiers:h}),role:"gridcell"},_=p.focusTarget&&a.isSameDay(p.focusTarget,e)&&!h.outside,j=p.focusedDay&&a.isSameDay(p.focusedDay,e),N=r(r(r({},k),((u={disabled:h.disabled,role:"gridcell"})["aria-selected"]=h.selected,u.tabIndex=j||_?0:-1,u)),m);return{isButton:x,isHidden:g,activeModifiers:h,selectedDays:b,buttonProps:N,divProps:k}}function ke(e){var a=t.useRef(null),o=we(e.date,e.displayMonth,a);return o.isHidden?n.jsx("div",{role:"gridcell"}):o.isButton?n.jsx(S,r({name:"day",ref:a},o.buttonProps)):n.jsx("div",r({},o.divProps))}function _e(e){var t=e.number,a=e.dates,o=y(),r=o.onWeekNumberClick,i=o.styles,s=o.classNames,l=o.locale,d=o.labels.labelWeekNumber,u=(0,o.formatters.formatWeekNumber)(Number(t),{locale:l});if(!r)return n.jsx("span",{className:s.weeknumber,style:i.weeknumber,children:u});var c=d(Number(t),{locale:l});return n.jsx(S,{name:"week-number","aria-label":c,className:s.weeknumber,style:i.weeknumber,onClick:function(e){r(t,a,e)},children:u})}function je(e){var t,o,r,i=y(),s=i.styles,l=i.classNames,d=i.showWeekNumber,u=i.components,c=null!==(t=null==u?void 0:u.Day)&&void 0!==t?t:ke,f=null!==(o=null==u?void 0:u.WeekNumber)&&void 0!==o?o:_e;return d&&(r=n.jsx("td",{className:l.cell,style:s.cell,children:n.jsx(f,{number:e.weekNumber,dates:e.dates})})),n.jsxs("tr",{className:l.row,style:s.row,children:[r,e.dates.map((function(t){return n.jsx("td",{className:l.cell,style:s.cell,role:"presentation",children:n.jsx(c,{displayMonth:e.displayMonth,date:t})},a.getUnixTime(t))}))]})}function Ne(e,n,t){for(var o=(null==t?void 0:t.ISOWeek)?a.endOfISOWeek(n):a.endOfWeek(n,t),r=(null==t?void 0:t.ISOWeek)?a.startOfISOWeek(e):a.startOfWeek(e,t),i=a.differenceInCalendarDays(o,r),s=[],l=0;l<=i;l++)s.push(a.addDays(r,l));return s.reduce((function(e,n){var o=(null==t?void 0:t.ISOWeek)?a.getISOWeek(n):a.getWeek(n,t),r=e.find((function(e){return e.weekNumber===o}));return r?(r.dates.push(n),e):(e.push({weekNumber:o,dates:[n]}),e)}),[])}function Ce(e){var t,o,r,i=y(),s=i.locale,l=i.classNames,d=i.styles,u=i.hideHead,c=i.fixedWeeks,f=i.components,v=i.weekStartsOn,p=i.firstWeekContainsDate,h=i.ISOWeek,m=function(e,n){var t=Ne(a.startOfMonth(e),a.endOfMonth(e),n);if(null==n?void 0:n.useFixedWeeks){var o=a.getWeeksInMonth(e,n);if(o<6){var r=t[t.length-1],i=r.dates[r.dates.length-1],s=a.addWeeks(i,6-o),l=Ne(a.addWeeks(i,1),s,n);t.push.apply(t,l)}}return t}(e.displayMonth,{useFixedWeeks:Boolean(c),ISOWeek:h,locale:s,weekStartsOn:v,firstWeekContainsDate:p}),b=null!==(t=null==f?void 0:f.Head)&&void 0!==t?t:E,x=null!==(o=null==f?void 0:f.Row)&&void 0!==o?o:je,D=null!==(r=null==f?void 0:f.Footer)&&void 0!==r?r:W;return n.jsxs("table",{id:e.id,className:l.table,style:d.table,role:"grid","aria-labelledby":e["aria-labelledby"],children:[!u&&n.jsx(b,{}),n.jsx("tbody",{className:l.tbody,style:d.tbody,children:m.map((function(t){return n.jsx(x,{displayMonth:e.displayMonth,dates:t.dates,weekNumber:t.weekNumber},t.weekNumber)}))}),n.jsx(D,{displayMonth:e.displayMonth})]})}var Se="undefined"!=typeof window&&window.document&&window.document.createElement?t.useLayoutEffect:t.useEffect,Oe=!1,Pe=0;function Ie(){return"react-day-picker-".concat(++Pe)}function We(e){var a,o,i=y(),s=i.dir,l=i.classNames,d=i.styles,u=i.components,c=_().displayMonths,f=function(e){var n,a=null!=e?e:Oe?Ie():null,o=t.useState(a),r=o[0],i=o[1];return Se((function(){null===r&&i(Ie())}),[]),t.useEffect((function(){!1===Oe&&(Oe=!0)}),[]),null!==(n=null!=e?e:r)&&void 0!==n?n:void 0}(i.id?"".concat(i.id,"-").concat(e.displayIndex):void 0),v=i.id?"".concat(i.id,"-grid-").concat(e.displayIndex):void 0,p=[l.month],h=d.month,m=0===e.displayIndex,b=e.displayIndex===c.length-1,x=!m&&!b;"rtl"===s&&(b=(a=[m,b])[0],m=a[1]),m&&(p.push(l.caption_start),h=r(r({},h),d.caption_start)),b&&(p.push(l.caption_end),h=r(r({},h),d.caption_end)),x&&(p.push(l.caption_between),h=r(r({},h),d.caption_between));var D=null!==(o=null==u?void 0:u.Caption)&&void 0!==o?o:I;return n.jsxs("div",{className:p.join(" "),style:h,children:[n.jsx(D,{id:f,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),n.jsx(Ce,{id:v,"aria-labelledby":f,displayMonth:e.displayMonth})]},e.displayIndex)}function Le(e){var t=y(),a=t.classNames,o=t.styles;return n.jsx("div",{className:a.months,style:o.months,children:e.children})}function Ee(e){var a,o,i=e.initialProps,s=y(),l=ye(),d=_(),u=t.useState(!1),c=u[0],f=u[1];t.useEffect((function(){s.initialFocus&&l.focusTarget&&(c||(l.focus(l.focusTarget),f(!0)))}),[s.initialFocus,c,l.focus,l.focusTarget,l]);var v=[s.classNames.root,s.className];s.numberOfMonths>1&&v.push(s.classNames.multiple_months),s.showWeekNumber&&v.push(s.classNames.with_weeknumber);var p=r(r({},s.styles.root),s.style),h=Object.keys(i).filter((function(e){return e.startsWith("data-")})).reduce((function(e,n){var t;return r(r({},e),((t={})[n]=i[n],t))}),{}),m=null!==(o=null===(a=i.components)||void 0===a?void 0:a.Months)&&void 0!==o?o:Le;return n.jsx("div",r({className:v.join(" "),style:p,dir:s.dir,id:s.id,nonce:i.nonce,title:i.title,lang:i.lang},h,{children:n.jsx(m,{children:d.displayMonths.map((function(e,t){return n.jsx(We,{displayIndex:t,displayMonth:e},t)}))})}))}function Be(e){var t=e.children,a=function(e,n){var t={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&n.indexOf(a)<0&&(t[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(a=Object.getOwnPropertySymbols(e);o<a.length;o++)n.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(t[a[o]]=e[a[o]])}return t}(e,["children"]);return n.jsx(h,{initialProps:a,children:n.jsx(k,{children:n.jsx(xe,{initialProps:a,children:n.jsx(R,{initialProps:a,children:n.jsx(q,{initialProps:a,children:n.jsx(ae,{children:n.jsx(he,{children:t})})})})})})})}function Fe(e){return!isNaN(e.getTime())}e.Button=S,e.Caption=I,e.CaptionDropdowns=j,e.CaptionLabel=m,e.CaptionNavigation=P,e.Day=ke,e.DayContent=B,e.DayPicker=function(e){return n.jsx(Be,r({},e,{children:n.jsx(Ee,{initialProps:e})}))},e.DayPickerContext=p,e.DayPickerProvider=h,e.Dropdown=x,e.FocusContext=pe,e.FocusProvider=he,e.Footer=W,e.Head=E,e.HeadRow=L,e.IconDropdown=b,e.IconLeft=N,e.IconRight=C,e.Months=Le,e.NavigationContext=w,e.NavigationProvider=k,e.RootProvider=Be,e.Row=je,e.SelectMultipleContext=F,e.SelectMultipleProvider=R,e.SelectMultipleProviderInternal=T,e.SelectRangeContext=U,e.SelectRangeProvider=q,e.SelectRangeProviderInternal=K,e.SelectSingleContext=be,e.SelectSingleProvider=xe,e.SelectSingleProviderInternal=De,e.WeekNumber=_e,e.addToRange=Y,e.isDateAfterType=se,e.isDateBeforeType=le,e.isDateInterval=re,e.isDateRange=ie,e.isDayOfWeekType=de,e.isDayPickerDefault=function(e){return void 0===e.mode||"default"===e.mode},e.isDayPickerMultiple=s,e.isDayPickerRange=l,e.isDayPickerSingle=d,e.isMatch=ue,e.useActiveModifiers=me,e.useDayPicker=y,e.useDayRender=we,e.useFocusContext=ye,e.useInput=function(e){void 0===e&&(e={});var n=e.locale,r=void 0===n?o.enUS:n,i=e.required,s=e.format,l=void 0===s?"PP":s,d=e.defaultSelected,u=e.today,c=void 0===u?new Date:u,f=v(e),p=f.fromDate,h=f.toDate,y=function(e){return a.parse(e,l,c,{locale:r})},m=t.useState(null!=d?d:c),b=m[0],x=m[1],D=t.useState(d),M=D[0],g=D[1],w=d?a.format(d,l,{locale:r}):"",k=t.useState(w),_=k[0],j=k[1],N=function(){g(d),x(null!=d?d:c),j(null!=w?w:"")};return{dayPickerProps:{month:b,onDayClick:function(e,n){var t=n.selected;if(!i&&t)return g(void 0),void j("");g(e),j(e?a.format(e,l,{locale:r}):"")},onMonthChange:function(e){x(e)},selected:M,locale:r,fromDate:p,toDate:h,today:c},inputProps:{onBlur:function(e){Fe(y(e.target.value))||N()},onChange:function(e){j(e.target.value);var n=y(e.target.value),t=p&&a.differenceInCalendarDays(p,n)>0,o=h&&a.differenceInCalendarDays(n,h)>0;!Fe(n)||t||o?g(void 0):(g(n),x(n))},onFocus:function(e){if(e.target.value){var n=y(e.target.value);Fe(n)&&x(n)}else N()},value:_,placeholder:a.format(new Date,l,{locale:r})},reset:N,setSelected:function(e){g(e),x(null!=e?e:c),j(e?a.format(e,l,{locale:r}):"")}}},e.useNavigation=_,e.useSelectMultiple=A,e.useSelectRange=z,e.useSelectSingle=Me}));
//# sourceMappingURL=index.min.js.map
