var A=function(E){return A=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(C){return typeof C}:function(C){return C&&typeof Symbol=="function"&&C.constructor===Symbol&&C!==Symbol.prototype?"symbol":typeof C},A(E)},$=function(E,C){var G=Object.keys(E);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(E);C&&(Z=Z.filter(function(K){return Object.getOwnPropertyDescriptor(E,K).enumerable})),G.push.apply(G,Z)}return G},z=function(E){for(var C=1;C<arguments.length;C++){var G=arguments[C]!=null?arguments[C]:{};C%2?$(Object(G),!0).forEach(function(Z){UH(E,Z,G[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(E,Object.getOwnPropertyDescriptors(G)):$(Object(G)).forEach(function(Z){Object.defineProperty(E,Z,Object.getOwnPropertyDescriptor(G,Z))})}return E},UH=function(E,C,G){if(C=XH(C),C in E)Object.defineProperty(E,C,{value:G,enumerable:!0,configurable:!0,writable:!0});else E[C]=G;return E},XH=function(E){var C=BH(E,"string");return A(C)=="symbol"?C:String(C)},BH=function(E,C){if(A(E)!="object"||!E)return E;var G=E[Symbol.toPrimitive];if(G!==void 0){var Z=G.call(E,C||"default");if(A(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(C==="string"?String:Number)(E)};(function(E){var C=Object.defineProperty,G=function H(X,U){for(var B in U)C(X,B,{get:U[B],enumerable:!0,configurable:!0,set:function J(Y){return U[B]=function(){return Y}}})},Z={lessThanXSeconds:{one:"menos de un segundo",other:"menos de {{count}} segundos"},xSeconds:{one:"1 segundo",other:"{{count}} segundos"},halfAMinute:"medio minuto",lessThanXMinutes:{one:"menos de un minuto",other:"menos de {{count}} minutos"},xMinutes:{one:"1 minuto",other:"{{count}} minutos"},aboutXHours:{one:"alrededor de 1 hora",other:"alrededor de {{count}} horas"},xHours:{one:"1 hora",other:"{{count}} horas"},xDays:{one:"1 d\xEDa",other:"{{count}} d\xEDas"},aboutXWeeks:{one:"alrededor de 1 semana",other:"alrededor de {{count}} semanas"},xWeeks:{one:"1 semana",other:"{{count}} semanas"},aboutXMonths:{one:"alrededor de 1 mes",other:"alrededor de {{count}} meses"},xMonths:{one:"1 mes",other:"{{count}} meses"},aboutXYears:{one:"alrededor de 1 a\xF1o",other:"alrededor de {{count}} a\xF1os"},xYears:{one:"1 a\xF1o",other:"{{count}} a\xF1os"},overXYears:{one:"m\xE1s de 1 a\xF1o",other:"m\xE1s de {{count}} a\xF1os"},almostXYears:{one:"casi 1 a\xF1o",other:"casi {{count}} a\xF1os"}},K=function H(X,U,B){var J,Y=Z[X];if(typeof Y==="string")J=Y;else if(U===1)J=Y.one;else J=Y.other.replace("{{count}}",U.toString());if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"en "+J;else return"hace "+J;return J};function N(H){return function(){var X=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},U=X.width?String(X.width):H.defaultWidth,B=H.formats[U]||H.formats[H.defaultWidth];return B}}var D={full:"EEEE, d 'de' MMMM 'de' y",long:"d 'de' MMMM 'de' y",medium:"d MMM y",short:"dd/MM/y"},M={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} 'a las' {{time}}",long:"{{date}} 'a las' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:N({formats:D,defaultWidth:"full"}),time:N({formats:M,defaultWidth:"full"}),dateTime:N({formats:S,defaultWidth:"full"})},L={lastWeek:"'el' eeee 'pasado a la' p",yesterday:"'ayer a la' p",today:"'hoy a la' p",tomorrow:"'ma\xF1ana a la' p",nextWeek:"eeee 'a la' p",other:"P"},V={lastWeek:"'el' eeee 'pasado a las' p",yesterday:"'ayer a las' p",today:"'hoy a las' p",tomorrow:"'ma\xF1ana a las' p",nextWeek:"eeee 'a las' p",other:"P"},j=function H(X,U,B,J){if(U.getHours()!==1)return V[X];else return L[X]};function Q(H){return function(X,U){var B=U!==null&&U!==void 0&&U.context?String(U.context):"standalone",J;if(B==="formatting"&&H.formattingValues){var Y=H.defaultFormattingWidth||H.defaultWidth,I=U!==null&&U!==void 0&&U.width?String(U.width):Y;J=H.formattingValues[I]||H.formattingValues[Y]}else{var T=H.defaultWidth,x=U!==null&&U!==void 0&&U.width?String(U.width):H.defaultWidth;J=H.values[x]||H.values[T]}var O=H.argumentCallback?H.argumentCallback(X):X;return J[O]}}var f={narrow:["AC","DC"],abbreviated:["AC","DC"],wide:["antes de cristo","despu\xE9s de cristo"]},v={narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xBA trimestre","2\xBA trimestre","3\xBA trimestre","4\xBA trimestre"]},_={narrow:["e","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["ene","feb","mar","abr","may","jun","jul","ago","sep","oct","nov","dic"],wide:["enero","febrero","marzo","abril","mayo","junio","julio","agosto","septiembre","octubre","noviembre","diciembre"]},F={narrow:["d","l","m","m","j","v","s"],short:["do","lu","ma","mi","ju","vi","s\xE1"],abbreviated:["dom","lun","mar","mi\xE9","jue","vie","s\xE1b"],wide:["domingo","lunes","martes","mi\xE9rcoles","jueves","viernes","s\xE1bado"]},w={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"ma\xF1ana",afternoon:"tarde",evening:"tarde",night:"noche"},abbreviated:{am:"AM",pm:"PM",midnight:"medianoche",noon:"mediodia",morning:"ma\xF1ana",afternoon:"tarde",evening:"tarde",night:"noche"},wide:{am:"a.m.",pm:"p.m.",midnight:"medianoche",noon:"mediodia",morning:"ma\xF1ana",afternoon:"tarde",evening:"tarde",night:"noche"}},P={narrow:{am:"a",pm:"p",midnight:"mn",noon:"md",morning:"de la ma\xF1ana",afternoon:"de la tarde",evening:"de la tarde",night:"de la noche"},abbreviated:{am:"AM",pm:"PM",midnight:"medianoche",noon:"mediodia",morning:"de la ma\xF1ana",afternoon:"de la tarde",evening:"de la tarde",night:"de la noche"},wide:{am:"a.m.",pm:"p.m.",midnight:"medianoche",noon:"mediodia",morning:"de la ma\xF1ana",afternoon:"de la tarde",evening:"de la tarde",night:"de la noche"}},b=function H(X,U){var B=Number(X);return B+"\xBA"},m={ordinalNumber:b,era:Q({values:f,defaultWidth:"wide"}),quarter:Q({values:v,defaultWidth:"wide",argumentCallback:function H(X){return Number(X)-1}}),month:Q({values:_,defaultWidth:"wide"}),day:Q({values:F,defaultWidth:"wide"}),dayPeriod:Q({values:w,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function k(H){return function(X){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=X.match(H.matchPattern);if(!B)return null;var J=B[0],Y=X.match(H.parsePattern);if(!Y)return null;var I=H.valueCallback?H.valueCallback(Y[0]):Y[0];I=U.valueCallback?U.valueCallback(I):I;var T=X.slice(J.length);return{value:I,rest:T}}}function q(H){return function(X){var U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=U.width,J=B&&H.matchPatterns[B]||H.matchPatterns[H.defaultMatchWidth],Y=X.match(J);if(!Y)return null;var I=Y[0],T=B&&H.parsePatterns[B]||H.parsePatterns[H.defaultParseWidth],x=Array.isArray(T)?c(T,function(W){return W.test(I)}):h(T,function(W){return W.test(I)}),O;O=H.valueCallback?H.valueCallback(x):x,O=U.valueCallback?U.valueCallback(O):O;var HH=X.slice(I.length);return{value:O,rest:HH}}}var h=function H(X,U){for(var B in X)if(Object.prototype.hasOwnProperty.call(X,B)&&U(X[B]))return B;return},c=function H(X,U){for(var B=0;B<X.length;B++)if(U(X[B]))return B;return},y=/^(\d+)(º)?/i,p=/\d+/i,d={narrow:/^(ac|dc|a|d)/i,abbreviated:/^(a\.?\s?c\.?|a\.?\s?e\.?\s?c\.?|d\.?\s?c\.?|e\.?\s?c\.?)/i,wide:/^(antes de cristo|antes de la era com[uú]n|despu[eé]s de cristo|era com[uú]n)/i},g={any:[/^ac/i,/^dc/i],wide:[/^(antes de cristo|antes de la era com[uú]n)/i,/^(despu[eé]s de cristo|era com[uú]n)/i]},u={narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^[1234](º)? trimestre/i},l={any:[/1/i,/2/i,/3/i,/4/i]},i={narrow:/^[efmajsond]/i,abbreviated:/^(ene|feb|mar|abr|may|jun|jul|ago|sep|oct|nov|dic)/i,wide:/^(enero|febrero|marzo|abril|mayo|junio|julio|agosto|septiembre|octubre|noviembre|diciembre)/i},n={narrow:[/^e/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^en/i,/^feb/i,/^mar/i,/^abr/i,/^may/i,/^jun/i,/^jul/i,/^ago/i,/^sep/i,/^oct/i,/^nov/i,/^dic/i]},s={narrow:/^[dlmjvs]/i,short:/^(do|lu|ma|mi|ju|vi|s[áa])/i,abbreviated:/^(dom|lun|mar|mi[ée]|jue|vie|s[áa]b)/i,wide:/^(domingo|lunes|martes|mi[ée]rcoles|jueves|viernes|s[áa]bado)/i},o={narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^do/i,/^lu/i,/^ma/i,/^mi/i,/^ju/i,/^vi/i,/^sa/i]},r={narrow:/^(a|p|mn|md|(de la|a las) (mañana|tarde|noche))/i,any:/^([ap]\.?\s?m\.?|medianoche|mediodia|(de la|a las) (mañana|tarde|noche))/i},e={any:{am:/^a/i,pm:/^p/i,midnight:/^mn/i,noon:/^md/i,morning:/mañana/i,afternoon:/tarde/i,evening:/tarde/i,night:/noche/i}},a={ordinalNumber:k({matchPattern:y,parsePattern:p,valueCallback:function H(X){return parseInt(X,10)}}),era:q({matchPatterns:d,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any"}),quarter:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any",valueCallback:function H(X){return X+1}}),month:q({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any"}),day:q({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:r,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},t={code:"es",formatDistance:K,formatLong:R,formatRelative:j,localize:m,match:a,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=z(z({},window.dateFns),{},{locale:z(z({},(E=window.dateFns)===null||E===void 0?void 0:E.locale),{},{es:t})})})();

//# debugId=4C45288B0B5B3F3564756e2164756e21
