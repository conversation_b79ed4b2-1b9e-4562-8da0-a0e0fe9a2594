// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nkyutlawmvsvxuffvjnw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5reXV0bGF3bXZzdnh1ZmZ2am53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwODE4MzUsImV4cCI6MjA2MDY1NzgzNX0.HXS2Dk0qohvq8EzVII6nXWzpeB0YgIq5VorhVkUuA90";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);